from onyx.configs.app_configs import ENCRYPTION_KEY_SECRET
from onyx.utils.logger import setup_logger
from onyx.utils.variable_functionality import fetch_versioned_implementation
from functools import lru_cache
from os import urandom

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.exceptions import InvalidTag


logger = setup_logger()

if ENCRYPTION_KEY_SECRET:
    logger.info("Encryption key configured. Encryption enabled.")

    @lru_cache(maxsize=1)
    def _get_valid_aes_key(key: str) -> bytes:
        key_bytes = key.encode()
        if len(key_bytes) < 16:
            raise ValueError("Encryption key is too short. Must be at least 16 bytes.")
        
        valid_lengths = [16, 24, 32]
        closest = min(valid_lengths, key=lambda x: abs(len(key_bytes) - x))
        return key_bytes[:closest]


    def encrypt_string_to_bytes(plaintext: str) -> bytes:
        if not ENCRYPTION_KEY_SECRET:
            raise RuntimeError("Encryption key not configured.")

        key = _get_valid_aes_key(ENCRYPTION_KEY_SECRET)
        nonce = urandom(12)

        cipher = Cipher(algorithms.AES(key), modes.GCM(nonce), backend=default_backend())
        encryptor = cipher.encryptor()

        ciphertext = encryptor.update(plaintext.encode()) + encryptor.finalize()
        return nonce + ciphertext + encryptor.tag


    def decrypt_bytes_to_string(ciphertext: bytes) -> str:
        if not ENCRYPTION_KEY_SECRET:
            raise RuntimeError("Encryption key not configured.")

        try:
            key = _get_valid_aes_key(ENCRYPTION_KEY_SECRET)

            # Handle legacy plaintext (not encrypted, just encoded)
            if not isinstance(ciphertext, (bytes, bytearray)) or len(ciphertext) < 16:
                logger.warning("Data too short, possibly legacy plaintext.")
                return ciphertext.decode()

            # Handle legacy AES-CBC (we expect IV + ciphertext, but not tag)
            if len(ciphertext) >= 32 and (len(ciphertext) - 16) % 16 == 0:
                try:
                    iv, ct = ciphertext[:16], ciphertext[16:]
                    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
                    decryptor = cipher.decryptor()
                    padded_plaintext = decryptor.update(ct) + decryptor.finalize()
                    unpadder = padding.PKCS7(algorithms.AES.block_size).unpadder()
                    plaintext = unpadder.update(padded_plaintext) + unpadder.finalize()
                    logger.info("Decrypted using legacy AES-CBC")
                    return plaintext.decode()
                except Exception:
                    pass  # fallback to AES-GCM below

            # Assume AES-GCM: nonce (12) + ciphertext + tag (16)
            if len(ciphertext) < 28:
                raise ValueError("Ciphertext too short for AES-GCM")

            nonce, ct_with_tag = ciphertext[:12], ciphertext[12:]
            tag = ct_with_tag[-16:]
            ct = ct_with_tag[:-16]

            cipher = Cipher(algorithms.AES(key), modes.GCM(nonce, tag), backend=default_backend())
            decryptor = cipher.decryptor()
            plaintext = decryptor.update(ct) + decryptor.finalize()
            return plaintext.decode()

        except (ValueError, UnicodeDecodeError, InvalidTag) as e:
            logger.warning(f"Decryption failed: {e}")
            try:
                return ciphertext.decode()
            except UnicodeDecodeError:
                raise RuntimeError(f"Failed to decrypt data: {e}") from e

else:
    logger.info("Encryption key not configured. Encryption disabled.")
    
    def _encrypt_string(input_str: str) -> bytes:
        if ENCRYPTION_KEY_SECRET:
            logger.warning("MIT version of Onyx does not support encryption of secrets.")
        return input_str.encode()
    

    def _decrypt_bytes(input_bytes: bytes) -> str:
        # No need to double warn. If you wish to learn more about encryption features
        # refer to the Onyx EE code
        return input_bytes.decode()
    

    def encrypt_string_to_bytes(intput_str: str) -> bytes:
        versioned_encryption_fn = fetch_versioned_implementation(
            "onyx.utils.encryption", "_encrypt_string"
        )
        return versioned_encryption_fn(intput_str)
    
    def decrypt_bytes_to_string(intput_bytes: bytes) -> str:
        versioned_decryption_fn = fetch_versioned_implementation(
            "onyx.utils.encryption", "_decrypt_bytes"
        )
        return versioned_decryption_fn(intput_bytes)